-- Found Git: C:/Program Files/Git/cmd/git.exe (found version "2.50.1.windows.1")
-- ccache will be used for faster recompilation
-- The C compiler identification is GNU 13.2.0
-- The CXX compiler identification is GNU 13.2.0
-- The ASM compiler identification is GNU
-- Found assembler: D:/esp32-idf-ahy/5.3.2/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/esp32-idf-ahy/5.3.2/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: D:/esp32-idf-ahy/5.3.2/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32
-- Project sdkconfig file C:/Users/<USER>/Desktop/esp/hello_world/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: d:/esp32-idf-ahy/5.3.2/python_env/idf5.3_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "hello_world" version: 1
-- Adding linker script C:/Users/<USER>/Desktop/esp/hello_world/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script C:/Users/<USER>/Desktop/esp/hello_world/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_rom/esp32/ld/esp32.rom.ld
-- Adding linker script D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_rom/esp32/ld/esp32.rom.api.ld
-- Adding linker script D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_rom/esp32/ld/esp32.rom.libgcc.ld
-- Adding linker script D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_rom/esp32/ld/esp32.rom.newlib-data.ld
-- Adding linker script D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_rom/esp32/ld/esp32.rom.syscalls.ld
-- Adding linker script D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_rom/esp32/ld/esp32.rom.newlib-funcs.ld
-- Adding linker script D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/soc/esp32/ld/esp32.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/app_trace D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/app_update D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/bootloader D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/bootloader_support D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/bt D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/cmock D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/console D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/cxx D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/driver D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/efuse D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp-tls D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_adc D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_app_format D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_bootloader_format D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_coex D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_common D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_ana_cmpr D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_cam D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_dac D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_gpio D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_gptimer D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_i2c D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_i2s D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_isp D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_jpeg D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_ledc D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_mcpwm D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_parlio D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_pcnt D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_ppa D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_rmt D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_sdio D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_sdm D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_sdmmc D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_sdspi D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_spi D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_touch_sens D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_tsens D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_uart D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_usb_serial_jtag D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_eth D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_event D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_gdbstub D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_hid D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_http_client D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_http_server D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_https_ota D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_https_server D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_hw_support D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_lcd D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_local_ctrl D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_mm D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_netif D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_netif_stack D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_partition D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_phy D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_pm D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_psram D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_ringbuf D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_rom D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_system D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_timer D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_vfs_console D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/espcoredump D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esptool_py D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/fatfs D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/freertos D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/hal D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/heap D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/http_parser D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/idf_test D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/ieee802154 D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/json D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/log D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/lwip C:/Users/<USER>/Desktop/esp/hello_world/main D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/mbedtls D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/mqtt D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/newlib D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/nvs_flash D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/nvs_sec_provider D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/openthread D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/partition_table D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/perfmon D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/protobuf-c D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/protocomm D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/pthread D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/sdmmc D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/soc D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/spi_flash D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/spiffs D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/tcp_transport D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/ulp D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/unity D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/usb D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/vfs D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/wear_levelling D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/wifi_provisioning D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/wpa_supplicant D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/xtensa
-- Configuring done (38.6s)
-- Generating done (3.1s)
-- Build files have been written to: C:/Users/<USER>/Desktop/esp/hello_world/build
