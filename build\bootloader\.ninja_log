# ninja log v6
68	432	7743722290364351	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	182fd0f92de8b23
78	560	7743722290477633	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	eb192f29bd33c7e1
31	624	7743722290003099	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	31340990319eb464
140	640	7743722291095188	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	c14847a5c5fd036e
124	692	7743722290938953	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	eb4cc971102759a
55	754	7743722290244357	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	a2e677d3d111d676
92	903	7743722290614741	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	a7ad7c3c8e83a4c5
44	977	7743722290129151	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	ed435fb428dd2590
255	1495	7743722292251917	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	c7ee6b23eb4bb242
203	1514	7743722291723744	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/esp_cpu_intr.c.obj	cfe598e9b52cd1c2
109	1529	7743722290784831	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	4dfb082b730698f5
186	1546	7743722291558126	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	45bb4cdc68a532d
272	1562	7743722292413834	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/cpu_region_protect.c.obj	4d25be6d72d5ac13
157	1577	7743722291268122	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	90f087d0c81be86d
389	1605	7743722293578670	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_init.c.obj	1cca4ce3fb9560e7
560	1625	7743722295298463	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/chip_info.c.obj	93453c30c78bc5ee
405	1642	7743722293759174	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_sleep.c.obj	d767ab93e7d6895c
640	1683	7743722296100749	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_table.c.obj	1c12f98958bd057a
435	1709	7743722294044694	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_time.c.obj	a1111a048a30e790
374	1724	7743722293436700	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk_init.c.obj	92a0a5d61c81ee34
693	1740	7743722296622590	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_fields.c.obj	f6e1f231745ff20d
625	1759	7743722295946157	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	33586466d225bfdc
321	1777	7743722292924043	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk.c.obj	2f36d8fc3491ad8b
757	1796	7743722297256193	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_utility.c.obj	42d35cc6777008a8
904	1825	7743722298741579	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	a9635e6f9949951e
1496	2258	7743722304680977	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	40d49af4d249b39e
1606	2316	7743722305766384	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	23bf93002f0f505f
1530	2376	7743722304991137	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c.obj	ecb5ce30e7986d76
977	2404	7743722299464746	esp-idf/log/liblog.a	fb42836df8e648d
1625	2901	7743722305942071	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	c381070d1488e7d7
1643	2915	7743722306123354	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	bbdafb5a4ec06362
1709	2931	7743722306781438	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	23e5fcb54e45af56
1724	2944	7743722306937684	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32.c.obj	750c7f431af6823e
1547	2959	7743722305163510	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	68787559a9db645b
1515	2974	7743722304844550	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	44947e6cd8432f83
1562	2993	7743722305318883	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	80a0af8595082be7
1578	3009	7743722305474416	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	79d2cd8a60087c4d
1825	3027	7743722307948094	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	e7282673085505e
1684	3044	7743722306538553	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	e9dda956cb39f112
1759	3059	7743722307288652	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	ef2cc45e47b6cf0f
1741	3079	7743722307107782	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	b0806fb2cd48e3b2
2376	3097	7743722313469200	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	4a72a7a43ce54c03
1778	3115	7743722307479603	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32.c.obj	503267d6f3c185c9
1797	3141	7743722307668455	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	8eb3f715fdd453a6
2316	3160	7743722312859605	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	e8933754d3466e53
2945	3205	7743722319140886	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_soc.c.obj	eee653af099fa368
2258	3289	7743722312275491	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	78199dfb4fd0e5ec
2915	3394	7743722318845743	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	d2c10307e6ea66e8
2932	3493	7743722319015839	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_sha.c.obj	f5c2e58b8228ca1a
3028	3508	7743722319976617	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	dfd4e15d93fb1ac4
2993	3525	7743722319619230	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	553bcc4cfa9a95a
2901	3540	7743722318709678	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	efcf698497c149f2
3044	3554	7743722320134404	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	8066a6351ffab470
2974	3589	7743722319438888	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	ecdec9d94b9fb495
3206	3608	7743722321751630	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	700a6e66a976331f
3012	3627	7743722319814261	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	e9782816d627a2b9
3059	3643	7743722320288277	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	ebed4a0f7e3cd4e7
3290	3710	7743722322587236	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	771e3df9e2ba55c1
2404	3755	7743722313738523	esp-idf/esp_rom/libesp_rom.a	249f4020de2b2e3d
3141	4444	7743722321108814	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/cache_hal_esp32.c.obj	cfb876fe1ccc68de
3080	4460	7743722320498283	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/efuse_hal.c.obj	a4d165c1a3f4805f
3394	4474	7743722323639954	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/interrupts.c.obj	f6a4261eec24542a
3116	4489	7743722320852144	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	42e90a77ba2f887a
3097	4504	7743722320669155	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	dcddb105c73d559f
2960	4520	7743722319284481	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_esp32.c.obj	1af866f1f1767376
3526	4535	7743722324954899	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dport_access.c.obj	26c1c434e4d5ad23
3540	4551	7743722325098621	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/adc_periph.c.obj	e17f19f498833b43
3493	4571	7743722324629053	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/gpio_periph.c.obj	feda8e5a7b868beb
3555	4589	7743722325233554	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/emac_periph.c.obj	d4e0c98d0d917741
3609	4605	7743722325778972	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/ledc_periph.c.obj	f3b141fd46f87d1e
3509	4623	7743722324779612	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/uart_periph.c.obj	a75a318fb43cfd1b
3589	4642	7743722325585698	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/spi_periph.c.obj	3534e07738cda0f4
3643	4668	7743722326129022	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rmt_periph.c.obj	73bc3f0d7ae91320
3711	4687	7743722326813414	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdm_periph.c.obj	8d8c37fb26ebaacf
3627	4706	7743722325968929	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/pcnt_periph.c.obj	eb28af94a0d5933b
4446	5222	7743722334153091	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2s_periph.c.obj	304d515d0a11a743
3161	5236	7743722321302834	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	cd2ed9948cc35c57
4460	5238	7743722334301901	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2c_periph.c.obj	248309a9758dd7d0
4474	5239	7743722334434957	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/timer_periph.c.obj	6a110e59893105f8
4489	5240	7743722334586937	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/lcd_periph.c.obj	d42a187672e9c29d
4505	5242	7743722334745136	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mcpwm_periph.c.obj	5dc35d85ac062cc6
4520	5243	7743722334898218	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mpi_periph.c.obj	a3ad13750a2f66a8
4535	5244	7743722335046026	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdmmc_periph.c.obj	48d24c874aca719f
4551	5245	7743722335225414	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/touch_sensor_periph.c.obj	b521d4d466d8276e
4571	5246	7743722335401670	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/twai_periph.c.obj	f95e34f78c944aaa
4590	5247	7743722335589298	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/wdt_periph.c.obj	9574d426e1b985b4
4606	5249	7743722335773093	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dac_periph.c.obj	98923cbb1341c59
4623	5250	7743722335933094	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rtc_io_periph.c.obj	762c9182aa5d8ee2
4642	5251	7743722336119566	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdio_slave_periph.c.obj	1dd4ed459090cee2
4668	5252	7743722336374957	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	8ac68af4c6ca080d
4687	5255	7743722336569123	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	7dbcbb360a81916f
3755	5255	7743722327246534	esp-idf/esp_common/libesp_common.a	6249deef2fb73c06
5222	5700	7743722341909602	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	9be847b56e0b34c
4706	5701	7743722345605698	project_elf_src_esp32.c	eb9d139625a3c13b
4706	5701	7743722345605698	C:/Users/<USER>/Desktop/esp/hello_world/build/bootloader/project_elf_src_esp32.c	eb9d139625a3c13b
5701	5877	7743722346708152	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32.c.obj	9bad0fcac4337745
5255	6149	7743722342241330	esp-idf/esp_hw_support/libesp_hw_support.a	8a4b278b0f4e0dea
6149	6742	7743722351182443	esp-idf/esp_system/libesp_system.a	819689569961b1cb
6742	7195	7743722357112256	esp-idf/efuse/libefuse.a	62db19714a3c911a
7195	7755	7743722361648492	esp-idf/bootloader_support/libbootloader_support.a	6b399df2ed9d72d3
7755	8358	7743722367247719	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	2dde16ce1a9bfd93
8358	9060	7743722373274685	esp-idf/spi_flash/libspi_flash.a	6dd9e3cdbbd7e536
9060	9562	7743722380298929	esp-idf/hal/libhal.a	334bcb58bf2a4bac
9562	9942	7743722385318850	esp-idf/micro-ecc/libmicro-ecc.a	3c7998d206265b5d
9942	10414	7743722389114784	esp-idf/soc/libsoc.a	88b68a7a0b76c48d
10414	10778	7743722393836063	esp-idf/xtensa/libxtensa.a	f2d227a0416107c2
10778	11173	7743722397480885	esp-idf/main/libmain.a	b36dccade6c6532e
11174	11732	7743722401427100	bootloader.elf	f3a82bc62b454143
11733	12536	7743722414898716	.bin_timestamp	7829c3492cf898d0
11733	12536	7743722414898716	C:/Users/<USER>/Desktop/esp/hello_world/build/bootloader/.bin_timestamp	7829c3492cf898d0
12537	12889	7743722415058716	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	9494b4158e71f772
12537	12889	7743722415058716	C:/Users/<USER>/Desktop/esp/hello_world/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	9494b4158e71f772
